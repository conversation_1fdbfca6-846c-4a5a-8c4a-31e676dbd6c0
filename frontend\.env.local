# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:5000
NEXT_PUBLIC_API_TIMEOUT=10000

# Blockchain Configuration
NEXT_PUBLIC_ETHEREUM_NETWORK=localhost
NEXT_PUBLIC_ETHEREUM_RPC_URL=http://127.0.0.1:8545
NEXT_PUBLIC_CHAIN_ID=1337

# Contract Addresses (will be populated after deployment)
NEXT_PUBLIC_ECOXCHANGE_TOKEN_ADDRESS=
NEXT_PUBLIC_ECOXCHANGE_MARKET_ADDRESS=
NEXT_PUBLIC_VALIDATOR_REGISTRY_ADDRESS=
NEXT_PUBLIC_COMPANY_ADDRESS=
NEXT_PUBLIC_DYNAMIC_PRICING_ADDRESS=

# IPFS Configuration
NEXT_PUBLIC_IPFS_GATEWAY=https://ipfs.io/ipfs/
NEXT_PUBLIC_IPFS_API_URL=http://localhost:5001

# Application Configuration
NEXT_PUBLIC_APP_NAME=EcoXChange
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_SUPPORT_EMAIL=<EMAIL>

# Feature Flags
NEXT_PUBLIC_ENABLE_DYNAMIC_PRICING=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_ANALYTICS=false

# Development Settings
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_SHOW_CONSOLE_LOGS=true
